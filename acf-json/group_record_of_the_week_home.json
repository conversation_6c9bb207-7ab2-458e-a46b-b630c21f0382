{"key": "group_record_of_the_week_home", "title": "Record of the Week Home", "fields": [{"key": "field_record_of_the_week_home_title", "label": "Block Title", "name": "block_title", "aria-label": "", "type": "text", "instructions": "Enter a title for this block", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Record of the Week", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_record_of_the_week_home_info", "label": "Record Source", "name": "record_source_info", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "This block automatically displays the current global Record of the Week with truncated description (5 lines max). No manual selection needed.", "new_lines": "wpautop", "esc_html": 0}, {"key": "field_record_of_the_week_home_background", "label": "Background Color", "name": "background_color", "aria-label": "", "type": "color_picker", "instructions": "Choose a background color for the block (optional)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "#1A1A1A", "enable_opacity": false, "return_format": "string"}, {"key": "field_record_of_the_week_home_vinyl_image", "label": "Vinyl image", "name": "vinyl_image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}, {"key": "field_record_of_the_week_home_button_link", "label": "Button Link", "name": "button_link", "aria-label": "", "type": "link", "instructions": "Optional button link to display at the bottom of the block", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}], "location": [[{"param": "block", "operator": "==", "value": "acf/record-of-the-week-home"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1747400900}