<?php
/**
 * Record of the Week functionality
 */

class Rewindrecords_Record_Of_The_Week {

    /**
     * Constructor
     */
    public function __construct() {
        // Hook to ensure only one active record of the week
        add_action('acf/save_post', array($this, 'ensure_single_active_record'), 20);
        
        // Add admin columns
        add_filter('manage_record_of_the_week_posts_columns', array($this, 'add_admin_columns'));
        add_action('manage_record_of_the_week_posts_custom_column', array($this, 'display_admin_columns'), 10, 2);
        
        // Make columns sortable
        add_filter('manage_edit-record_of_the_week_sortable_columns', array($this, 'make_columns_sortable'));
        add_action('pre_get_posts', array($this, 'handle_column_sorting'));
    }

    /**
     * Ensure only one record of the week is active at a time
     */
    public function ensure_single_active_record($post_id) {
        // Only run for record_of_the_week post type
        if (get_post_type($post_id) !== 'record_of_the_week') {
            return;
        }

        // Check if this record is being set as active
        $is_active = get_field('is_active', $post_id);
        
        if ($is_active) {
            // Deactivate all other records of the week
            $other_records = get_posts(array(
                'post_type' => 'record_of_the_week',
                'posts_per_page' => -1,
                'post__not_in' => array($post_id),
                'meta_query' => array(
                    array(
                        'key' => 'is_active',
                        'value' => '1',
                        'compare' => '='
                    )
                )
            ));

            foreach ($other_records as $record) {
                update_field('is_active', false, $record->ID);
            }
        }
    }

    /**
     * Get the current active record of the week (newest first)
     */
    public static function get_current_record() {
        $records = get_posts(array(
            'post_type' => 'record_of_the_week',
            'posts_per_page' => 1,
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        return !empty($records) ? $records[0] : null;
    }

    /**
     * Get all records of the week (archive)
     */
    public static function get_archive($limit = -1) {
        return get_posts(array(
            'post_type' => 'record_of_the_week',
            'posts_per_page' => $limit,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
    }

    /**
     * Get the selected record for a record of the week
     */
    public static function get_selected_record($rotw_id) {
        return get_field('selected_record', $rotw_id);
    }

    /**
     * Add admin columns
     */
    public function add_admin_columns($columns) {
        // Remove date column and add our custom columns
        unset($columns['date']);
        
        $columns['selected_record'] = __('Selected Record', 'rewindrecords');
        $columns['rotw_date'] = __('Date', 'rewindrecords');
        $columns['is_active'] = __('Active', 'rewindrecords');
        $columns['date'] = __('Created', 'rewindrecords');
        
        return $columns;
    }

    /**
     * Display admin columns
     */
    public function display_admin_columns($column, $post_id) {
        switch ($column) {
            case 'selected_record':
                $record = get_field('selected_record', $post_id);
                if ($record) {
                    echo '<strong>' . esc_html($record->post_title) . '</strong>';
                } else {
                    echo '<em>' . __('No record selected', 'rewindrecords') . '</em>';
                }
                break;
                
            case 'rotw_date':
                $rotw_date = get_field('rotw_date', $post_id);

                if ($rotw_date) {
                    echo esc_html(date('d/m/Y', strtotime($rotw_date)));
                } else {
                    echo '<em>' . __('No date set', 'rewindrecords') . '</em>';
                }
                break;
                
            case 'is_active':
                $is_active = get_field('is_active', $post_id);
                if ($is_active) {
                    echo '<span style="color: green; font-weight: bold;">✓ ' . __('Active', 'rewindrecords') . '</span>';
                } else {
                    echo '<span style="color: #666;">○ ' . __('Inactive', 'rewindrecords') . '</span>';
                }
                break;
        }
    }

    /**
     * Make columns sortable
     */
    public function make_columns_sortable($columns) {
        $columns['rotw_date'] = 'rotw_date';
        $columns['is_active'] = 'is_active';
        return $columns;
    }

    /**
     * Handle column sorting
     */
    public function handle_column_sorting($query) {
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }

        if ($query->get('post_type') !== 'record_of_the_week') {
            return;
        }

        $orderby = $query->get('orderby');

        if ($orderby === 'rotw_date') {
            $query->set('meta_key', 'rotw_date');
            $query->set('orderby', 'meta_value');
        } elseif ($orderby === 'is_active') {
            $query->set('meta_key', 'is_active');
            $query->set('orderby', 'meta_value');
        }
    }

    /**
     * Get record of the week data for display
     */
    public static function get_display_data($rotw_id = null) {
        if (!$rotw_id) {
            $current_record = self::get_current_record();
            if (!$current_record) {
                return null;
            }
            $rotw_id = $current_record->ID;
        }

        $selected_record = get_field('selected_record', $rotw_id);
        $description = get_field('description', $rotw_id);
        $rotw_date = get_field('rotw_date', $rotw_id);

        if (!$selected_record) {
            return null;
        }

        return array(
            'rotw_id' => $rotw_id,
            'record' => $selected_record,
            'description' => $description,
            'rotw_date' => $rotw_date,
            'title' => get_the_title($rotw_id)
        );
    }
}
