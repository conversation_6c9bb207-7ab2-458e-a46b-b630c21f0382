<?php
/**
 * Block Name: Record Picks Slider
 * Description: Display a slider with manually selected records from your collection
 */

// Get block values
$title = get_field('title');
$selected_records = get_field('selected_records');
$show_artist = get_field('show_artist');
$show_year = get_field('show_year');
$show_format = get_field('show_format');
$link = get_field('link');

// Create unique ID for this block
$block_id = 'record-picks-slider-' . $block['id'];
if (!empty($block['anchor'])) {
    $block_id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values
$classes = 'randomRecordsSlider';
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}

// Check if we have selected records
if (empty($selected_records)) {
    if (is_admin()) {
        echo '<div class="acf-block-placeholder"><p>' . __('Please select records to display in the slider.', 'rewindrecords') . '</p></div>';
    }
    return;
}

// Get the selected record posts
$record_ids = array();
foreach ($selected_records as $record) {
    if (is_object($record) && isset($record->ID)) {
        $record_ids[] = $record->ID;
    } elseif (is_array($record) && isset($record['ID'])) {
        $record_ids[] = $record['ID'];
    } elseif (is_numeric($record)) {
        $record_ids[] = $record;
    }
}

if (empty($record_ids)) {
    if (is_admin()) {
        echo '<div class="acf-block-placeholder"><p>' . __('No valid records selected.', 'rewindrecords') . '</p></div>';
    }
    return;
}

// Query the selected records
$selected_records_query = new WP_Query(array(
    'post_type' => 'record',
    'post__in' => $record_ids,
    'orderby' => 'post__in',
    'posts_per_page' => count($record_ids),
    'meta_query' => array(
        'relation' => 'OR',
        array(
            'key' => '_record_stock',
            'value' => '0',
            'compare' => '>',
            'type' => 'NUMERIC'
        ),
        array(
            'key' => '_record_stock',
            'compare' => 'NOT EXISTS'
        )
    ),
));
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init>
    <div class="contentWrapper">
        <div class="col">
            <?php if ($title) : ?>
                <h2 class="normalTitle" data-lines data-words><?php echo esc_html($title); ?></h2>
            <?php endif; ?>
        </div>
        <?php if ($link) : ?>
        <div class="col right">
            <a href="<?php echo esc_url($link['url']); ?>" title="<?php echo esc_html($link['title']);  ?>" class="textLink" <?php echo $link['target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                <i class="icon-arrow-right"></i>
                <span class="innerMask">
                    <span class="innerWrapper">
                        <span class="innerText absolute"><?php echo esc_html($link['title']); ?></span>
                        <span class="innerText absolute" aria-hidden="true"><?php echo esc_html($link['title']); ?></span>
                    </span>
                </span>
                <span class="divider">
                    <svg xmlns="http://www.w3.org/2000/svg" width="184.5" height="2" viewBox="0 0 184.5 2">
                        <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                    </svg>
                </span>
            </a>
        </div>
        <?php endif; ?>
        <div class="sliderWrapper">
            <?php if ($selected_records_query->have_posts()) : ?>
                <div class="recordsSlider">
                    <?php
                    while ($selected_records_query->have_posts()) : $selected_records_query->the_post();
                        // Wrap the record item in a slide div
                        echo '<div class="recordSlide slide">';
                        rewindrecords_render_record_item(get_the_ID(), array(
                            'image_size' => 'record-cover-optimized',
                            'show_artist' => $show_artist,
                            'show_year' => $show_year,
                            'show_format' => true,
                            'show_price' => true,
                            'lazy_load' => true,
                        ));
                        echo '</div>';
                    endwhile;
                    ?>
                </div>
            <?php else : ?>
                <div class="noRecords">
                    <p><?php _e('No records found or all selected records are out of stock.', 'rewindrecords'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php wp_reset_postdata(); ?>

<style>
.recordPicksSliderBlock {
    padding: 80px 0;
    background: #000;
    color: #fff;
}

.recordPicksSliderBlock .contentWrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.recordPicksSliderBlock .col {
    display: inline-block;
    vertical-align: top;
    width: 48%;
}

.recordPicksSliderBlock .col.right {
    text-align: right;
}

.recordPicksSliderBlock .normalTitle {
    font-family: 'Bebas Neue', sans-serif;
    font-size: 48px;
    line-height: 1.2;
    margin: 0 0 20px 0;
    color: #fff;
}

.recordPicksSliderBlock .textLink {
    color: #fff;
    text-decoration: none;
    font-family: 'Bebas Neue', sans-serif;
    font-size: 18px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.recordPicksSliderBlock .sliderWrapper {
    margin-top: 40px;
}

.recordPicksSliderBlock .recordsSlider {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding: 20px 0;
}

.recordPicksSliderBlock .recordSlide {
    flex: 0 0 auto;
    width: 280px;
}

.recordPicksSliderBlock .noRecords {
    text-align: center;
    padding: 40px 20px;
    color: #ccc;
}

@media (max-width: 1160px) {
    .recordPicksSliderBlock .col {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .recordPicksSliderBlock .col.right {
        text-align: left;
    }
    
    .recordPicksSliderBlock .normalTitle {
        font-size: 36px;
    }
}

@media (max-width: 580px) {
    .recordPicksSliderBlock {
        padding: 40px 0;
    }
    
    .recordPicksSliderBlock .normalTitle {
        font-size: 28px;
    }
    
    .recordPicksSliderBlock .recordSlide {
        width: 220px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.querySelector('#<?php echo esc_js($block_id); ?> .recordsSlider');
    if (slider && typeof window.Flickity !== 'undefined') {
        new Flickity(slider, {
            cellAlign: 'left',
            contain: true,
            prevNextButtons: true,
            pageDots: false,
            groupCells: true,
            wrapAround: false
        });
    }
});
</script>
